using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;
using System.Text;

namespace ZeroDateStrat.Services;

public interface IPerformanceAnalytics
{
    PerformanceReport GeneratePerformanceReport(BacktestResult result);
    string GenerateDetailedReport(BacktestResult result);
    List<PerformanceMetric> CalculateAdvancedMetrics(BacktestResult result);
    RiskMetrics CalculateRiskMetrics(BacktestResult result);
    List<MonthlyPerformance> CalculateMonthlyPerformance(BacktestResult result);
    TradeAnalysis AnalyzeTrades(BacktestResult result);
}

public class PerformanceAnalytics : IPerformanceAnalytics
{
    private readonly ILogger<PerformanceAnalytics> _logger;

    public PerformanceAnalytics(ILogger<PerformanceAnalytics> logger)
    {
        _logger = logger;
    }

    public PerformanceReport GeneratePerformanceReport(BacktestResult result)
    {
        try
        {
            var report = new PerformanceReport
            {
                StrategyName = result.StrategyName,
                StartDate = result.StartDate,
                EndDate = result.EndDate,
                TotalReturn = result.TotalReturn,
                AnnualizedReturn = result.AnnualizedReturn,
                MaxDrawdown = result.MaxDrawdown,
                SharpeRatio = result.SharpeRatio,
                SortinoRatio = result.SortinoRatio,
                CalmarRatio = result.CalmarRatio,
                WinRate = result.WinRate,
                ProfitFactor = result.ProfitFactor,
                TotalTrades = result.TotalTrades,
                AdvancedMetrics = CalculateAdvancedMetrics(result),
                RiskMetrics = CalculateRiskMetrics(result),
                MonthlyPerformance = CalculateMonthlyPerformance(result),
                TradeAnalysis = AnalyzeTrades(result)
            };

            return report;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating performance report");
            throw;
        }
    }

    public string GenerateDetailedReport(BacktestResult result)
    {
        try
        {
            var report = GeneratePerformanceReport(result);
            var sb = new StringBuilder();

            sb.AppendLine("=== BACKTESTING PERFORMANCE REPORT ===");
            sb.AppendLine($"Strategy: {report.StrategyName}");
            sb.AppendLine($"Period: {report.StartDate:yyyy-MM-dd} to {report.EndDate:yyyy-MM-dd}");
            sb.AppendLine($"Duration: {(report.EndDate - report.StartDate).TotalDays:F0} days");
            sb.AppendLine();

            // Overall Performance
            sb.AppendLine("=== OVERALL PERFORMANCE ===");
            sb.AppendLine($"Initial Capital: {result.InitialCapital:C2}");
            sb.AppendLine($"Final Capital: {result.FinalCapital:C2}");
            sb.AppendLine($"Total Return: {report.TotalReturn:P2}");
            sb.AppendLine($"Annualized Return: {report.AnnualizedReturn:P2}");
            sb.AppendLine($"Maximum Drawdown: {report.MaxDrawdown:P2}");
            sb.AppendLine();

            // Risk Metrics
            sb.AppendLine("=== RISK METRICS ===");
            sb.AppendLine($"Sharpe Ratio: {report.SharpeRatio:F3}");
            sb.AppendLine($"Sortino Ratio: {report.SortinoRatio:F3}");
            sb.AppendLine($"Calmar Ratio: {report.CalmarRatio:F3}");
            sb.AppendLine($"Value at Risk (95%): {report.RiskMetrics.ValueAtRisk95:P2}");
            sb.AppendLine($"Expected Shortfall (95%): {report.RiskMetrics.ExpectedShortfall95:P2}");
            sb.AppendLine($"Volatility (Annualized): {report.RiskMetrics.AnnualizedVolatility:P2}");
            sb.AppendLine();

            // Trade Statistics
            sb.AppendLine("=== TRADE STATISTICS ===");
            sb.AppendLine($"Total Trades: {report.TotalTrades}");
            sb.AppendLine($"Winning Trades: {result.WinningTrades} ({report.WinRate:P1})");
            sb.AppendLine($"Losing Trades: {result.LosingTrades} ({(1 - report.WinRate):P1})");
            sb.AppendLine($"Average Win: {result.AverageWin:C2}");
            sb.AppendLine($"Average Loss: {result.AverageLoss:C2}");
            sb.AppendLine($"Profit Factor: {report.ProfitFactor:F2}");
            sb.AppendLine($"Largest Win: {result.LargestWin:C2}");
            sb.AppendLine($"Largest Loss: {result.LargestLoss:C2}");
            sb.AppendLine($"Average Holding Period: {result.AverageHoldingPeriod}");
            sb.AppendLine();

            // Advanced Metrics
            sb.AppendLine("=== ADVANCED METRICS ===");
            foreach (var metric in report.AdvancedMetrics)
            {
                sb.AppendLine($"{metric.Name}: {metric.Value:F4} ({metric.Description})");
            }
            sb.AppendLine();

            // Monthly Performance
            sb.AppendLine("=== MONTHLY PERFORMANCE ===");
            sb.AppendLine("Month\t\tReturn\t\tDrawdown\tTrades");
            foreach (var monthly in report.MonthlyPerformance)
            {
                sb.AppendLine($"{monthly.Month:yyyy-MM}\t{monthly.Return:P2}\t\t{monthly.MaxDrawdown:P2}\t\t{monthly.TradeCount}");
            }
            sb.AppendLine();

            // Trade Analysis by Strategy
            sb.AppendLine("=== TRADE ANALYSIS BY STRATEGY ===");
            foreach (var strategy in report.TradeAnalysis.StrategyBreakdown)
            {
                sb.AppendLine($"{strategy.Key}:");
                sb.AppendLine($"  Trades: {strategy.Value.TradeCount}");
                sb.AppendLine($"  Win Rate: {strategy.Value.WinRate:P1}");
                sb.AppendLine($"  Avg PnL: {strategy.Value.AveragePnL:C2}");
                sb.AppendLine($"  Total PnL: {strategy.Value.TotalPnL:C2}");
            }
            sb.AppendLine();

            // Best and Worst Trades
            sb.AppendLine("=== BEST TRADES ===");
            var bestTrades = result.Trades.OrderByDescending(t => t.RealizedPnL).Take(5);
            foreach (var trade in bestTrades)
            {
                sb.AppendLine($"{trade.EntryTime:yyyy-MM-dd} {trade.Strategy} {trade.UnderlyingSymbol}: {trade.RealizedPnL:C2} ({trade.ExitReason})");
            }
            sb.AppendLine();

            sb.AppendLine("=== WORST TRADES ===");
            var worstTrades = result.Trades.OrderBy(t => t.RealizedPnL).Take(5);
            foreach (var trade in worstTrades)
            {
                sb.AppendLine($"{trade.EntryTime:yyyy-MM-dd} {trade.Strategy} {trade.UnderlyingSymbol}: {trade.RealizedPnL:C2} ({trade.ExitReason})");
            }

            return sb.ToString();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating detailed report");
            throw;
        }
    }

    public List<PerformanceMetric> CalculateAdvancedMetrics(BacktestResult result)
    {
        var metrics = new List<PerformanceMetric>();

        try
        {
            // Information Ratio
            var benchmarkReturn = 0.10m; // Assume 10% benchmark return
            var trackingError = CalculateTrackingError(result, benchmarkReturn);
            var informationRatio = trackingError > 0 ? (result.AnnualizedReturn - benchmarkReturn) / trackingError : 0;
            
            metrics.Add(new PerformanceMetric
            {
                Name = "Information Ratio",
                Value = (double)informationRatio,
                Description = "Excess return per unit of tracking error"
            });

            // Omega Ratio
            var omegaRatio = CalculateOmegaRatio(result);
            metrics.Add(new PerformanceMetric
            {
                Name = "Omega Ratio",
                Value = (double)omegaRatio,
                Description = "Probability-weighted ratio of gains vs losses"
            });

            // Tail Ratio
            var tailRatio = CalculateTailRatio(result);
            metrics.Add(new PerformanceMetric
            {
                Name = "Tail Ratio",
                Value = (double)tailRatio,
                Description = "95th percentile return / 5th percentile return"
            });

            // Recovery Factor
            var recoveryFactor = result.MaxDrawdown > 0 ? result.TotalReturn / result.MaxDrawdown : 0;
            metrics.Add(new PerformanceMetric
            {
                Name = "Recovery Factor",
                Value = (double)recoveryFactor,
                Description = "Total return divided by maximum drawdown"
            });

            // Expectancy
            var expectancy = CalculateExpectancy(result);
            metrics.Add(new PerformanceMetric
            {
                Name = "Expectancy",
                Value = (double)expectancy,
                Description = "Average expected profit per trade"
            });

            // Kelly Criterion
            var kellyCriterion = CalculateKellyCriterion(result);
            metrics.Add(new PerformanceMetric
            {
                Name = "Kelly Criterion",
                Value = (double)kellyCriterion,
                Description = "Optimal position size based on win rate and payoffs"
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating advanced metrics");
        }

        return metrics;
    }

    public RiskMetrics CalculateRiskMetrics(BacktestResult result)
    {
        try
        {
            var dailyReturns = result.DailyPerformance.Select(d => d.DailyReturn).ToList();
            
            // Value at Risk (VaR)
            var sortedReturns = dailyReturns.OrderBy(r => r).ToList();
            var var95Index = (int)(sortedReturns.Count * 0.05);
            var var99Index = (int)(sortedReturns.Count * 0.01);
            
            var var95 = sortedReturns.Count > var95Index ? sortedReturns[var95Index] : 0;
            var var99 = sortedReturns.Count > var99Index ? sortedReturns[var99Index] : 0;

            // Expected Shortfall (Conditional VaR)
            var es95 = sortedReturns.Take(var95Index + 1).Average();
            var es99 = sortedReturns.Take(var99Index + 1).Average();

            // Volatility
            var avgReturn = dailyReturns.Average();
            var variance = dailyReturns.Sum(r => (r - avgReturn) * (r - avgReturn)) / (dailyReturns.Count - 1);
            var dailyVolatility = (decimal)Math.Sqrt((double)variance);
            var annualizedVolatility = dailyVolatility * (decimal)Math.Sqrt(252);

            // Downside Deviation
            var downsideReturns = dailyReturns.Where(r => r < 0).ToList();
            var downsideDeviation = downsideReturns.Any() ? 
                (decimal)Math.Sqrt((double)downsideReturns.Sum(r => r * r) / downsideReturns.Count) * (decimal)Math.Sqrt(252) : 0;

            // Sortino Ratio
            var sortinoRatio = downsideDeviation > 0 ? result.AnnualizedReturn / downsideDeviation : 0;

            return new RiskMetrics
            {
                ValueAtRisk95 = var95,
                ValueAtRisk99 = var99,
                ExpectedShortfall95 = es95,
                ExpectedShortfall99 = es99,
                AnnualizedVolatility = annualizedVolatility,
                DownsideDeviation = downsideDeviation,
                SortinoRatio = sortinoRatio,
                MaxDrawdownDuration = CalculateMaxDrawdownDuration(result)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating risk metrics");
            return new RiskMetrics();
        }
    }

    public List<MonthlyPerformance> CalculateMonthlyPerformance(BacktestResult result)
    {
        try
        {
            var monthlyPerformance = new List<MonthlyPerformance>();
            var groupedByMonth = result.DailyPerformance.GroupBy(d => new { d.Date.Year, d.Date.Month });

            foreach (var month in groupedByMonth)
            {
                var monthlyData = month.ToList();
                var startValue = monthlyData.First().PortfolioValue;
                var endValue = monthlyData.Last().PortfolioValue;
                var monthlyReturn = startValue > 0 ? (endValue - startValue) / startValue : 0;
                var maxDrawdown = monthlyData.Max(d => d.Drawdown);
                
                var monthlyTrades = result.Trades.Count(t => 
                    t.EntryTime.Year == month.Key.Year && t.EntryTime.Month == month.Key.Month);

                monthlyPerformance.Add(new MonthlyPerformance
                {
                    Month = new DateTime(month.Key.Year, month.Key.Month, 1),
                    Return = monthlyReturn,
                    MaxDrawdown = maxDrawdown,
                    TradeCount = monthlyTrades,
                    StartValue = startValue,
                    EndValue = endValue
                });
            }

            return monthlyPerformance.OrderBy(m => m.Month).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating monthly performance");
            return new List<MonthlyPerformance>();
        }
    }

    public TradeAnalysis AnalyzeTrades(BacktestResult result)
    {
        try
        {
            var analysis = new TradeAnalysis();

            // Strategy breakdown
            var strategyGroups = result.Trades.GroupBy(t => t.Strategy);
            foreach (var group in strategyGroups)
            {
                var trades = group.ToList();
                analysis.StrategyBreakdown[group.Key] = new StrategyPerformance
                {
                    TradeCount = trades.Count,
                    WinRate = trades.Count > 0 ? (decimal)trades.Count(t => t.RealizedPnL > 0) / trades.Count : 0,
                    AveragePnL = trades.Any() ? trades.Average(t => t.RealizedPnL) : 0,
                    TotalPnL = trades.Sum(t => t.RealizedPnL)
                };
            }

            // Exit reason analysis
            var exitReasonGroups = result.Trades.GroupBy(t => t.ExitReason);
            foreach (var group in exitReasonGroups)
            {
                analysis.ExitReasonBreakdown[group.Key] = group.Count();
            }

            // Holding period analysis
            var holdingPeriods = result.Trades.Where(t => t.ExitTime.HasValue).Select(t => t.HoldingPeriod.TotalHours).ToList();
            if (holdingPeriods.Any())
            {
                analysis.AverageHoldingPeriodHours = holdingPeriods.Average();
                analysis.MedianHoldingPeriodHours = holdingPeriods.OrderBy(h => h).Skip(holdingPeriods.Count / 2).First();
            }

            // Consecutive wins/losses
            analysis.MaxConsecutiveWins = CalculateMaxConsecutiveWins(result.Trades);
            analysis.MaxConsecutiveLosses = CalculateMaxConsecutiveLosses(result.Trades);

            return analysis;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error analyzing trades");
            return new TradeAnalysis();
        }
    }

    private decimal CalculateTrackingError(BacktestResult result, decimal benchmarkReturn)
    {
        var dailyBenchmarkReturn = benchmarkReturn / 252; // Daily benchmark return
        var trackingDifferences = result.DailyPerformance.Select(d => d.DailyReturn - dailyBenchmarkReturn).ToList();
        
        if (!trackingDifferences.Any()) return 0;
        
        var avgDifference = trackingDifferences.Average();
        var variance = trackingDifferences.Sum(d => (d - avgDifference) * (d - avgDifference)) / (trackingDifferences.Count - 1);
        return (decimal)Math.Sqrt((double)variance) * (decimal)Math.Sqrt(252);
    }

    private decimal CalculateOmegaRatio(BacktestResult result, decimal threshold = 0)
    {
        var returns = result.DailyPerformance.Select(d => d.DailyReturn).ToList();
        var gainsSum = returns.Where(r => r > threshold).Sum(r => r - threshold);
        var lossesSum = returns.Where(r => r <= threshold).Sum(r => threshold - r);
        
        return lossesSum > 0 ? gainsSum / lossesSum : 0;
    }

    private decimal CalculateTailRatio(BacktestResult result)
    {
        var returns = result.DailyPerformance.Select(d => d.DailyReturn).OrderBy(r => r).ToList();
        if (returns.Count < 20) return 0;
        
        var percentile95Index = (int)(returns.Count * 0.95);
        var percentile5Index = (int)(returns.Count * 0.05);
        
        var percentile95 = returns[percentile95Index];
        var percentile5 = returns[percentile5Index];
        
        return percentile5 != 0 ? Math.Abs(percentile95 / percentile5) : 0;
    }

    private decimal CalculateExpectancy(BacktestResult result)
    {
        if (result.TotalTrades == 0) return 0;
        
        var winRate = result.WinRate;
        var avgWin = result.AverageWin;
        var avgLoss = result.AverageLoss;
        
        return (winRate * avgWin) + ((1 - winRate) * avgLoss);
    }

    private decimal CalculateKellyCriterion(BacktestResult result)
    {
        if (result.TotalTrades == 0 || result.AverageLoss == 0) return 0;
        
        var winRate = result.WinRate;
        var avgWin = result.AverageWin;
        var avgLoss = Math.Abs(result.AverageLoss);
        
        return (winRate / avgLoss) - ((1 - winRate) / avgWin);
    }

    private int CalculateMaxDrawdownDuration(BacktestResult result)
    {
        var maxDuration = 0;
        var currentDuration = 0;
        var peak = result.InitialCapital;
        
        foreach (var daily in result.DailyPerformance)
        {
            if (daily.PortfolioValue > peak)
            {
                peak = daily.PortfolioValue;
                currentDuration = 0;
            }
            else
            {
                currentDuration++;
                maxDuration = Math.Max(maxDuration, currentDuration);
            }
        }
        
        return maxDuration;
    }

    private int CalculateMaxConsecutiveWins(List<BacktestTrade> trades)
    {
        var maxWins = 0;
        var currentWins = 0;
        
        foreach (var trade in trades.OrderBy(t => t.EntryTime))
        {
            if (trade.RealizedPnL > 0)
            {
                currentWins++;
                maxWins = Math.Max(maxWins, currentWins);
            }
            else
            {
                currentWins = 0;
            }
        }
        
        return maxWins;
    }

    private int CalculateMaxConsecutiveLosses(List<BacktestTrade> trades)
    {
        var maxLosses = 0;
        var currentLosses = 0;
        
        foreach (var trade in trades.OrderBy(t => t.EntryTime))
        {
            if (trade.RealizedPnL < 0)
            {
                currentLosses++;
                maxLosses = Math.Max(maxLosses, currentLosses);
            }
            else
            {
                currentLosses = 0;
            }
        }
        
        return maxLosses;
    }
}

// Supporting models for performance analytics
public class PerformanceReport
{
    public string StrategyName { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public decimal TotalReturn { get; set; }
    public decimal AnnualizedReturn { get; set; }
    public decimal MaxDrawdown { get; set; }
    public decimal SharpeRatio { get; set; }
    public decimal SortinoRatio { get; set; }
    public decimal CalmarRatio { get; set; }
    public decimal WinRate { get; set; }
    public decimal ProfitFactor { get; set; }
    public int TotalTrades { get; set; }
    public List<PerformanceMetric> AdvancedMetrics { get; set; } = new();
    public RiskMetrics RiskMetrics { get; set; } = new();
    public List<MonthlyPerformance> MonthlyPerformance { get; set; } = new();
    public TradeAnalysis TradeAnalysis { get; set; } = new();
}

public class PerformanceMetric
{
    public string Name { get; set; } = string.Empty;
    public double Value { get; set; }
    public string Description { get; set; } = string.Empty;
}

public class RiskMetrics
{
    public decimal ValueAtRisk95 { get; set; }
    public decimal ValueAtRisk99 { get; set; }
    public decimal ExpectedShortfall95 { get; set; }
    public decimal ExpectedShortfall99 { get; set; }
    public decimal AnnualizedVolatility { get; set; }
    public decimal DownsideDeviation { get; set; }
    public decimal SortinoRatio { get; set; }
    public int MaxDrawdownDuration { get; set; }
}

public class MonthlyPerformance
{
    public DateTime Month { get; set; }
    public decimal Return { get; set; }
    public decimal MaxDrawdown { get; set; }
    public int TradeCount { get; set; }
    public decimal StartValue { get; set; }
    public decimal EndValue { get; set; }
}

public class TradeAnalysis
{
    public Dictionary<string, StrategyPerformance> StrategyBreakdown { get; set; } = new();
    public Dictionary<string, int> ExitReasonBreakdown { get; set; } = new();
    public double AverageHoldingPeriodHours { get; set; }
    public double MedianHoldingPeriodHours { get; set; }
    public int MaxConsecutiveWins { get; set; }
    public int MaxConsecutiveLosses { get; set; }
}

public class StrategyPerformance
{
    public int TradeCount { get; set; }
    public decimal WinRate { get; set; }
    public decimal AveragePnL { get; set; }
    public decimal TotalPnL { get; set; }
}
