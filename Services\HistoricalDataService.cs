using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;

namespace ZeroDateStrat.Services;

public interface IHistoricalDataService
{
    Task<List<HistoricalBar>> GetHistoricalBarsAsync(string symbol, DateTime startDate, DateTime endDate, TimeFrame timeFrame = TimeFrame.Daily);
    Task<List<OptionContract>> GetHistoricalOptionChainAsync(string symbol, DateTime date);
    Task<decimal> GetHistoricalPriceAsync(string symbol, DateTime dateTime);
    Task<List<HistoricalVolatility>> GetHistoricalVolatilityAsync(string symbol, DateTime startDate, DateTime endDate);
    Task<bool> IsMarketOpenAsync(DateTime dateTime);
}

public class HistoricalDataService : IHistoricalDataService
{
    private readonly ILogger<HistoricalDataService> _logger;
    private readonly IConfiguration _configuration;
    private readonly IAlpacaService _alpacaService;

    public HistoricalDataService(ILogger<HistoricalDataService> logger, IConfiguration configuration, IAlpacaService alpacaService)
    {
        _logger = logger;
        _configuration = configuration;
        _alpacaService = alpacaService;
    }

    public async Task<List<HistoricalBar>> GetHistoricalBarsAsync(string symbol, DateTime startDate, DateTime endDate, TimeFrame timeFrame = TimeFrame.Daily)
    {
        try
        {
            _logger.LogDebug($"Fetching historical bars for {symbol} from {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd}");

            // For now, generate synthetic historical data based on current market patterns
            // In production, you'd integrate with a proper historical data provider
            var bars = new List<HistoricalBar>();
            var currentDate = startDate;
            var random = new Random(symbol.GetHashCode()); // Deterministic for backtesting
            
            // Get a base price for the symbol
            var basePrice = GetBasePriceForSymbol(symbol);
            var currentPrice = basePrice;

            while (currentDate <= endDate)
            {
                // Skip weekends
                if (currentDate.DayOfWeek != DayOfWeek.Saturday && currentDate.DayOfWeek != DayOfWeek.Sunday)
                {
                    // Generate realistic price movement
                    var dailyReturn = (decimal)(random.NextGaussian() * 0.02); // 2% daily volatility
                    var open = currentPrice;
                    var close = open * (1 + dailyReturn);
                    var high = Math.Max(open, close) * (1 + (decimal)(random.NextDouble() * 0.01));
                    var low = Math.Min(open, close) * (1 - (decimal)(random.NextDouble() * 0.01));
                    var volume = (long)(random.Next(1000000, 10000000));

                    bars.Add(new HistoricalBar
                    {
                        Symbol = symbol,
                        Date = currentDate,
                        Open = open,
                        High = high,
                        Low = low,
                        Close = close,
                        Volume = volume,
                        TimeFrame = timeFrame
                    });

                    currentPrice = close;
                }

                currentDate = timeFrame switch
                {
                    TimeFrame.Minute => currentDate.AddMinutes(1),
                    TimeFrame.Hour => currentDate.AddHours(1),
                    TimeFrame.Daily => currentDate.AddDays(1),
                    _ => currentDate.AddDays(1)
                };
            }

            _logger.LogDebug($"Generated {bars.Count} historical bars for {symbol}");
            return bars;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error fetching historical bars for {symbol}");
            return new List<HistoricalBar>();
        }
    }

    public async Task<List<OptionContract>> GetHistoricalOptionChainAsync(string symbol, DateTime date)
    {
        try
        {
            _logger.LogDebug($"Fetching historical option chain for {symbol} on {date:yyyy-MM-dd}");

            // Get historical underlying price
            var underlyingPrice = await GetHistoricalPriceAsync(symbol, date);
            if (underlyingPrice <= 0)
                return new List<OptionContract>();

            // Generate historical option chain (simplified)
            var contracts = new List<OptionContract>();
            var expirationDate = GetNextExpirationDate(date);
            var timeToExpiry = (decimal)(expirationDate - date).TotalDays / 365.25m;
            var impliedVol = GetHistoricalImpliedVolatility(symbol, date);

            // Generate strikes around the underlying price
            for (int i = -10; i <= 10; i++)
            {
                var strike = Math.Round((underlyingPrice + (i * 5)) / 5) * 5;
                
                // Calculate option prices using Black-Scholes
                var callPrice = CalculateHistoricalOptionPrice(underlyingPrice, strike, timeToExpiry, impliedVol, true);
                var putPrice = CalculateHistoricalOptionPrice(underlyingPrice, strike, timeToExpiry, impliedVol, false);
                var callDelta = CalculateHistoricalDelta(underlyingPrice, strike, timeToExpiry, impliedVol, true);
                var putDelta = CalculateHistoricalDelta(underlyingPrice, strike, timeToExpiry, impliedVol, false);

                // Call option
                contracts.Add(new OptionContract
                {
                    Symbol = $"{symbol}{expirationDate:yyMMdd}C{strike:00000000}",
                    UnderlyingSymbol = symbol,
                    ExpirationDate = expirationDate,
                    StrikePrice = strike,
                    OptionType = Models.OptionType.Call,
                    Bid = callPrice * 0.95m,
                    Ask = callPrice * 1.05m,
                    LastPrice = callPrice,
                    Volume = GetRealisticVolume(underlyingPrice / strike),
                    OpenInterest = GetRealisticOpenInterest(underlyingPrice / strike),
                    Delta = callDelta,
                    ImpliedVolatility = impliedVol,
                    LastUpdated = date
                });

                // Put option
                contracts.Add(new OptionContract
                {
                    Symbol = $"{symbol}{expirationDate:yyMMdd}P{strike:00000000}",
                    UnderlyingSymbol = symbol,
                    ExpirationDate = expirationDate,
                    StrikePrice = strike,
                    OptionType = Models.OptionType.Put,
                    Bid = putPrice * 0.95m,
                    Ask = putPrice * 1.05m,
                    LastPrice = putPrice,
                    Volume = GetRealisticVolume(underlyingPrice / strike),
                    OpenInterest = GetRealisticOpenInterest(underlyingPrice / strike),
                    Delta = putDelta,
                    ImpliedVolatility = impliedVol,
                    LastUpdated = date
                });
            }

            return contracts;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error fetching historical option chain for {symbol} on {date:yyyy-MM-dd}");
            return new List<OptionContract>();
        }
    }

    public async Task<decimal> GetHistoricalPriceAsync(string symbol, DateTime dateTime)
    {
        try
        {
            var bars = await GetHistoricalBarsAsync(symbol, dateTime.Date, dateTime.Date);
            return bars.FirstOrDefault()?.Close ?? GetBasePriceForSymbol(symbol);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error getting historical price for {symbol} on {dateTime:yyyy-MM-dd}");
            return 0;
        }
    }

    public async Task<List<HistoricalVolatility>> GetHistoricalVolatilityAsync(string symbol, DateTime startDate, DateTime endDate)
    {
        try
        {
            var bars = await GetHistoricalBarsAsync(symbol, startDate, endDate);
            var volatilities = new List<HistoricalVolatility>();

            for (int i = 20; i < bars.Count; i++) // Need at least 20 days for calculation
            {
                var recentBars = bars.Skip(i - 20).Take(20).ToList();
                var returns = new List<decimal>();

                for (int j = 1; j < recentBars.Count; j++)
                {
                    var dailyReturn = (decimal)Math.Log((double)(recentBars[j].Close / recentBars[j - 1].Close));
                    returns.Add(dailyReturn);
                }

                var avgReturn = returns.Average();
                var variance = returns.Sum(r => (r - avgReturn) * (r - avgReturn)) / (returns.Count - 1);
                var volatility = (decimal)Math.Sqrt((double)variance) * (decimal)Math.Sqrt(252); // Annualized

                volatilities.Add(new HistoricalVolatility
                {
                    Symbol = symbol,
                    Date = bars[i].Date,
                    RealizedVolatility = volatility,
                    ImpliedVolatility = volatility * 1.1m // IV typically higher than RV
                });
            }

            return volatilities;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error calculating historical volatility for {symbol}");
            return new List<HistoricalVolatility>();
        }
    }

    public async Task<bool> IsMarketOpenAsync(DateTime dateTime)
    {
        // Simple market hours check (9:30 AM - 4:00 PM ET, weekdays)
        if (dateTime.DayOfWeek == DayOfWeek.Saturday || dateTime.DayOfWeek == DayOfWeek.Sunday)
            return false;

        var marketOpen = new TimeSpan(9, 30, 0);
        var marketClose = new TimeSpan(16, 0, 0);
        
        return dateTime.TimeOfDay >= marketOpen && dateTime.TimeOfDay <= marketClose;
    }

    private decimal GetBasePriceForSymbol(string symbol)
    {
        return symbol switch
        {
            "SPY" => 450m,
            "SPX" => 4500m,
            "QQQ" => 380m,
            "IWM" => 200m,
            "VIX" => 18.5m,
            _ => 100m
        };
    }

    private DateTime GetNextExpirationDate(DateTime date)
    {
        // For 0 DTE, return the same date if it's a trading day
        if (date.DayOfWeek != DayOfWeek.Saturday && date.DayOfWeek != DayOfWeek.Sunday)
            return date;
        
        // Otherwise, find next trading day
        var nextDate = date.AddDays(1);
        while (nextDate.DayOfWeek == DayOfWeek.Saturday || nextDate.DayOfWeek == DayOfWeek.Sunday)
        {
            nextDate = nextDate.AddDays(1);
        }
        return nextDate;
    }

    private decimal GetHistoricalImpliedVolatility(string symbol, DateTime date)
    {
        // Simplified IV calculation based on symbol and market conditions
        var baseIV = symbol switch
        {
            "SPY" or "SPX" => 0.15m,
            "QQQ" => 0.20m,
            "IWM" => 0.25m,
            _ => 0.20m
        };

        // Add some randomness based on date for realistic variation
        var random = new Random(date.GetHashCode());
        var variation = (decimal)(random.NextGaussian() * 0.05);
        return Math.Max(0.05m, baseIV + variation);
    }

    private decimal CalculateHistoricalOptionPrice(decimal spotPrice, decimal strikePrice, decimal timeToExpiry, decimal volatility, bool isCall)
    {
        if (timeToExpiry <= 0)
            return Math.Max(0, isCall ? spotPrice - strikePrice : strikePrice - spotPrice);

        var riskFreeRate = 0.05m;
        var d1 = (decimal)(Math.Log((double)(spotPrice / strikePrice)) + (double)(riskFreeRate + volatility * volatility / 2) * (double)timeToExpiry) / (decimal)((double)volatility * Math.Sqrt((double)timeToExpiry));
        var d2 = d1 - (decimal)((double)volatility * Math.Sqrt((double)timeToExpiry));

        var nd1 = NormalCDF((double)d1);
        var nd2 = NormalCDF((double)d2);
        var nMinusD1 = NormalCDF(-(double)d1);
        var nMinusD2 = NormalCDF(-(double)d2);

        if (isCall)
        {
            return spotPrice * (decimal)nd1 - strikePrice * (decimal)Math.Exp(-(double)riskFreeRate * (double)timeToExpiry) * (decimal)nd2;
        }
        else
        {
            return strikePrice * (decimal)Math.Exp(-(double)riskFreeRate * (double)timeToExpiry) * (decimal)nMinusD2 - spotPrice * (decimal)nMinusD1;
        }
    }

    private decimal CalculateHistoricalDelta(decimal spotPrice, decimal strikePrice, decimal timeToExpiry, decimal volatility, bool isCall)
    {
        if (timeToExpiry <= 0)
            return isCall ? (spotPrice > strikePrice ? 1 : 0) : (spotPrice < strikePrice ? -1 : 0);

        var riskFreeRate = 0.05m;
        var d1 = (decimal)(Math.Log((double)(spotPrice / strikePrice)) + (double)(riskFreeRate + volatility * volatility / 2) * (double)timeToExpiry) / (decimal)((double)volatility * Math.Sqrt((double)timeToExpiry));

        return isCall ? (decimal)NormalCDF((double)d1) : (decimal)NormalCDF((double)d1) - 1;
    }

    private static double NormalCDF(double x)
    {
        return 0.5 * (1 + Math.Sign(x) * Math.Sqrt(1 - Math.Exp(-2 * x * x / Math.PI)));
    }

    private decimal GetRealisticVolume(decimal moneyness)
    {
        var atmDistance = Math.Abs(1 - moneyness);
        var baseVolume = 100m;
        
        if (atmDistance < 0.02m) return baseVolume * 5;
        if (atmDistance < 0.05m) return baseVolume * 3;
        if (atmDistance < 0.10m) return baseVolume * 2;
        return baseVolume;
    }

    private decimal GetRealisticOpenInterest(decimal moneyness)
    {
        var atmDistance = Math.Abs(1 - moneyness);
        var baseOI = 500m;
        
        if (atmDistance < 0.02m) return baseOI * 4;
        if (atmDistance < 0.05m) return baseOI * 2.5m;
        if (atmDistance < 0.10m) return baseOI * 1.5m;
        return baseOI;
    }
}

// Supporting models
public class HistoricalBar
{
    public string Symbol { get; set; } = string.Empty;
    public DateTime Date { get; set; }
    public decimal Open { get; set; }
    public decimal High { get; set; }
    public decimal Low { get; set; }
    public decimal Close { get; set; }
    public long Volume { get; set; }
    public TimeFrame TimeFrame { get; set; }
}

public class HistoricalVolatility
{
    public string Symbol { get; set; } = string.Empty;
    public DateTime Date { get; set; }
    public decimal RealizedVolatility { get; set; }
    public decimal ImpliedVolatility { get; set; }
}

public enum TimeFrame
{
    Minute,
    Hour,
    Daily
}

// Extension method for Gaussian random numbers
public static class RandomExtensions
{
    public static double NextGaussian(this Random random, double mean = 0, double stdDev = 1)
    {
        var u1 = 1.0 - random.NextDouble();
        var u2 = 1.0 - random.NextDouble();
        var randStdNormal = Math.Sqrt(-2.0 * Math.Log(u1)) * Math.Sin(2.0 * Math.PI * u2);
        return mean + stdDev * randStdNormal;
    }
}
