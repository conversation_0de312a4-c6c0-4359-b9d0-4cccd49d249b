using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ZeroDateStrat.Models;
using ZeroDateStrat.Strategies;
using ZeroDateStrat.Utils;

namespace ZeroDateStrat.Services;

public interface IBacktestingEngine
{
    Task<BacktestResult> RunBacktestAsync(BacktestConfiguration config);
    Task<List<BacktestResult>> RunParameterOptimizationAsync(ParameterOptimizationConfig config);
    Task<BacktestResult> RunWalkForwardAnalysisAsync(WalkForwardConfig config);
}

public class BacktestingEngine : IBacktestingEngine
{
    private readonly ILogger<BacktestingEngine> _logger;
    private readonly IConfiguration _configuration;
    private readonly IHistoricalDataService _historicalDataService;
    private readonly IOptionsScanner _optionsScanner;
    private readonly IRiskManager _riskManager;

    public BacktestingEngine(
        ILogger<BacktestingEngine> logger,
        IConfiguration configuration,
        IHistoricalDataService historicalDataService,
        IOptionsScanner optionsScanner,
        IRiskManager riskManager)
    {
        _logger = logger;
        _configuration = configuration;
        _historicalDataService = historicalDataService;
        _optionsScanner = optionsScanner;
        _riskManager = riskManager;
    }

    public async Task<BacktestResult> RunBacktestAsync(BacktestConfiguration config)
    {
        try
        {
            _logger.LogInformation($"Starting backtest for {config.StrategyName} from {config.StartDate:yyyy-MM-dd} to {config.EndDate:yyyy-MM-dd}");

            var result = new BacktestResult
            {
                StrategyName = config.StrategyName,
                StartDate = config.StartDate,
                EndDate = config.EndDate,
                InitialCapital = config.InitialCapital,
                Parameters = config.Parameters
            };

            var portfolio = new BacktestPortfolio(config.InitialCapital);
            var currentDate = config.StartDate;
            var activePositions = new List<BacktestPosition>();

            while (currentDate <= config.EndDate)
            {
                // Skip weekends and holidays
                if (!await _historicalDataService.IsMarketOpenAsync(currentDate))
                {
                    currentDate = currentDate.AddDays(1);
                    continue;
                }

                try
                {
                    // Update portfolio with current market values
                    await UpdatePortfolioValues(portfolio, activePositions, currentDate);

                    // Manage existing positions
                    await ManageExistingPositions(activePositions, currentDate, result);

                    // Look for new trading opportunities
                    if (ShouldLookForNewTrades(currentDate, config))
                    {
                        var signals = await GenerateSignalsForDate(config.Symbols, currentDate, config);
                        
                        foreach (var signal in signals)
                        {
                            if (await ValidateSignalForBacktest(signal, portfolio, config))
                            {
                                var position = await ExecuteSignalInBacktest(signal, currentDate, portfolio, config);
                                if (position != null)
                                {
                                    activePositions.Add(position);
                                    result.Trades.Add(CreateBacktestTrade(position, signal));
                                }
                            }
                        }
                    }

                    // Record daily performance
                    var dailyPerf = new DailyPerformance
                    {
                        Date = currentDate,
                        PortfolioValue = portfolio.TotalValue,
                        DailyReturn = portfolio.DailyReturn,
                        CumulativeReturn = (portfolio.TotalValue - config.InitialCapital) / config.InitialCapital,
                        ActivePositions = activePositions.Count(p => p.Status == PositionStatus.Open),
                        DailyPnL = portfolio.DailyPnL
                    };
                    result.DailyPerformance.Add(dailyPerf);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Error processing backtest for date {currentDate:yyyy-MM-dd}");
                }

                currentDate = currentDate.AddDays(1);
            }

            // Calculate final metrics
            CalculateBacktestMetrics(result, portfolio);

            _logger.LogInformation($"Backtest completed. Total trades: {result.TotalTrades}, Win rate: {result.WinRate:P2}, Total return: {result.TotalReturn:P2}");
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error running backtest");
            throw;
        }
    }

    public async Task<List<BacktestResult>> RunParameterOptimizationAsync(ParameterOptimizationConfig config)
    {
        try
        {
            _logger.LogInformation($"Starting parameter optimization for {config.StrategyName}");

            var results = new List<BacktestResult>();
            var parameterCombinations = GenerateParameterCombinations(config.ParameterRanges);

            foreach (var parameters in parameterCombinations)
            {
                var backtestConfig = new BacktestConfiguration
                {
                    StrategyName = config.StrategyName,
                    StartDate = config.StartDate,
                    EndDate = config.EndDate,
                    InitialCapital = config.InitialCapital,
                    Symbols = config.Symbols,
                    Parameters = parameters
                };

                var result = await RunBacktestAsync(backtestConfig);
                results.Add(result);

                _logger.LogDebug($"Optimization run completed: {string.Join(", ", parameters.Select(p => $"{p.Key}={p.Value}"))} - Return: {result.TotalReturn:P2}");
            }

            // Sort by optimization metric (e.g., Sharpe ratio, total return, etc.)
            var sortedResults = results.OrderByDescending(r => GetOptimizationScore(r, config.OptimizationMetric)).ToList();

            _logger.LogInformation($"Parameter optimization completed. Best result: {sortedResults.First().TotalReturn:P2} return");
            return sortedResults;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error running parameter optimization");
            throw;
        }
    }

    public async Task<BacktestResult> RunWalkForwardAnalysisAsync(WalkForwardConfig config)
    {
        try
        {
            _logger.LogInformation($"Starting walk-forward analysis for {config.StrategyName}");

            var combinedResult = new BacktestResult
            {
                StrategyName = config.StrategyName + "_WalkForward",
                StartDate = config.StartDate,
                EndDate = config.EndDate,
                InitialCapital = config.InitialCapital
            };

            var currentDate = config.StartDate;
            var totalCapital = config.InitialCapital;

            while (currentDate.AddDays(config.InSampleDays + config.OutOfSampleDays) <= config.EndDate)
            {
                // In-sample period for optimization
                var inSampleStart = currentDate;
                var inSampleEnd = currentDate.AddDays(config.InSampleDays);

                // Out-of-sample period for testing
                var outOfSampleStart = inSampleEnd.AddDays(1);
                var outOfSampleEnd = outOfSampleStart.AddDays(config.OutOfSampleDays);

                // Run optimization on in-sample data
                var optimizationConfig = new ParameterOptimizationConfig
                {
                    StrategyName = config.StrategyName,
                    StartDate = inSampleStart,
                    EndDate = inSampleEnd,
                    InitialCapital = totalCapital,
                    Symbols = config.Symbols,
                    ParameterRanges = config.ParameterRanges,
                    OptimizationMetric = config.OptimizationMetric
                };

                var optimizationResults = await RunParameterOptimizationAsync(optimizationConfig);
                var bestParameters = optimizationResults.First().Parameters;

                // Test on out-of-sample data
                var testConfig = new BacktestConfiguration
                {
                    StrategyName = config.StrategyName,
                    StartDate = outOfSampleStart,
                    EndDate = outOfSampleEnd,
                    InitialCapital = totalCapital,
                    Symbols = config.Symbols,
                    Parameters = bestParameters
                };

                var testResult = await RunBacktestAsync(testConfig);

                // Combine results
                combinedResult.Trades.AddRange(testResult.Trades);
                combinedResult.DailyPerformance.AddRange(testResult.DailyPerformance);
                totalCapital = testResult.FinalCapital;

                currentDate = outOfSampleEnd.AddDays(1);
            }

            combinedResult.FinalCapital = totalCapital;
            CalculateBacktestMetrics(combinedResult, new BacktestPortfolio(totalCapital));

            _logger.LogInformation($"Walk-forward analysis completed. Final return: {combinedResult.TotalReturn:P2}");
            return combinedResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error running walk-forward analysis");
            throw;
        }
    }

    private async Task UpdatePortfolioValues(BacktestPortfolio portfolio, List<BacktestPosition> positions, DateTime date)
    {
        var previousValue = portfolio.TotalValue;
        portfolio.Cash = portfolio.InitialCapital;

        foreach (var position in positions.Where(p => p.Status == PositionStatus.Open))
        {
            // Update position value based on current market prices
            var currentValue = await CalculatePositionValue(position, date);
            position.CurrentValue = currentValue;
            position.UnrealizedPnL = currentValue - position.EntryValue;
            
            portfolio.Cash -= position.EntryValue; // Reduce cash by position cost
        }

        portfolio.TotalValue = portfolio.Cash + positions.Where(p => p.Status == PositionStatus.Open).Sum(p => p.CurrentValue);
        portfolio.DailyReturn = previousValue > 0 ? (portfolio.TotalValue - previousValue) / previousValue : 0;
        portfolio.DailyPnL = portfolio.TotalValue - previousValue;
    }

    private async Task<decimal> CalculatePositionValue(BacktestPosition position, DateTime date)
    {
        try
        {
            decimal totalValue = 0;

            foreach (var leg in position.Legs)
            {
                // Get historical option chain for the date
                var optionChain = await _historicalDataService.GetHistoricalOptionChainAsync(leg.UnderlyingSymbol, date);
                var option = optionChain.FirstOrDefault(o => 
                    o.OptionType == leg.OptionType && 
                    Math.Abs(o.StrikePrice - leg.StrikePrice) < 0.01m);

                if (option != null)
                {
                    var legValue = option.LastPrice * leg.Quantity;
                    if (leg.Side == OrderSide.Sell)
                        legValue = -legValue;
                    
                    totalValue += legValue;
                }
            }

            return totalValue;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error calculating position value for {position.Id}");
            return position.CurrentValue; // Return last known value
        }
    }

    private async Task ManageExistingPositions(List<BacktestPosition> positions, DateTime date, BacktestResult result)
    {
        var positionsToClose = new List<BacktestPosition>();

        foreach (var position in positions.Where(p => p.Status == PositionStatus.Open))
        {
            // Check exit conditions
            var shouldClose = false;
            var exitReason = "";

            // Profit target
            if (position.UnrealizedPnL >= position.ProfitTarget)
            {
                shouldClose = true;
                exitReason = "Profit Target";
            }
            // Stop loss
            else if (position.UnrealizedPnL <= -position.StopLoss)
            {
                shouldClose = true;
                exitReason = "Stop Loss";
            }
            // Expiration
            else if (date.Date >= position.ExpirationDate.Date)
            {
                shouldClose = true;
                exitReason = "Expiration";
            }
            // Time-based exit (e.g., 50% of time to expiration)
            else if ((position.ExpirationDate - date).TotalHours <= 4) // Close 4 hours before expiration
            {
                shouldClose = true;
                exitReason = "Time Decay";
            }

            if (shouldClose)
            {
                position.Status = PositionStatus.Closed;
                position.ExitTime = date;
                position.ExitValue = position.CurrentValue;
                position.RealizedPnL = position.UnrealizedPnL;
                position.ExitReason = exitReason;

                // Update trade record
                var trade = result.Trades.FirstOrDefault(t => t.Id == position.Id);
                if (trade != null)
                {
                    trade.ExitTime = date;
                    trade.ExitPrice = position.ExitValue;
                    trade.RealizedPnL = position.RealizedPnL;
                    trade.ExitReason = exitReason;
                }

                positionsToClose.Add(position);
            }
        }

        foreach (var position in positionsToClose)
        {
            _logger.LogDebug($"Closed position {position.Id}: {position.ExitReason}, PnL: {position.RealizedPnL:C2}");
        }
    }

    private bool ShouldLookForNewTrades(DateTime date, BacktestConfiguration config)
    {
        // Only look for new trades during specific hours (e.g., 9:45 AM - 10:30 AM)
        var entryStart = new TimeSpan(9, 45, 0);
        var entryEnd = new TimeSpan(10, 30, 0);
        
        return date.TimeOfDay >= entryStart && date.TimeOfDay <= entryEnd;
    }

    private async Task<List<TradingSignal>> GenerateSignalsForDate(List<string> symbols, DateTime date, BacktestConfiguration config)
    {
        var signals = new List<TradingSignal>();

        foreach (var symbol in symbols)
        {
            try
            {
                // Get historical option chain for the date
                var optionChain = await _historicalDataService.GetHistoricalOptionChainAsync(symbol, date);
                if (optionChain.Any())
                {
                    var chain = new OptionChain
                    {
                        UnderlyingSymbol = symbol,
                        ExpirationDate = date, // 0 DTE
                        Calls = optionChain.Where(o => o.OptionType == Models.OptionType.Call).ToList(),
                        Puts = optionChain.Where(o => o.OptionType == Models.OptionType.Put).ToList()
                    };

                    // Generate signals using the options scanner
                    var chainSignals = await _optionsScanner.FindTradingOpportunitiesAsync(new List<OptionChain> { chain });
                    signals.AddRange(chainSignals);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error generating signals for {symbol} on {date:yyyy-MM-dd}");
            }
        }

        return signals;
    }

    private async Task<bool> ValidateSignalForBacktest(TradingSignal signal, BacktestPortfolio portfolio, BacktestConfiguration config)
    {
        // Basic validation
        if (!signal.IsValid)
            return false;

        // Check if we have enough capital
        var requiredCapital = Math.Abs(signal.MaxLoss);
        if (requiredCapital > portfolio.Cash * 0.1m) // Don't risk more than 10% per trade
            return false;

        // Additional risk checks can be added here
        return true;
    }

    private async Task<BacktestPosition?> ExecuteSignalInBacktest(TradingSignal signal, DateTime date, BacktestPortfolio portfolio, BacktestConfiguration config)
    {
        try
        {
            var position = new BacktestPosition
            {
                Id = signal.Id,
                Strategy = signal.Strategy,
                UnderlyingSymbol = signal.UnderlyingSymbol,
                Legs = signal.Legs,
                EntryTime = date,
                EntryValue = signal.Legs.Sum(l => l.Price * l.Quantity * (l.Side == OrderSide.Buy ? 1 : -1)),
                ExpirationDate = signal.ExpirationDate,
                ProfitTarget = signal.ExpectedProfit * 0.5m, // Take profit at 50% of max profit
                StopLoss = Math.Abs(signal.MaxLoss) * 0.8m, // Stop loss at 80% of max loss
                Status = PositionStatus.Open
            };

            position.CurrentValue = position.EntryValue;
            return position;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error executing signal {signal.Id} in backtest");
            return null;
        }
    }

    private BacktestTrade CreateBacktestTrade(BacktestPosition position, TradingSignal signal)
    {
        return new BacktestTrade
        {
            Id = position.Id,
            Strategy = position.Strategy,
            UnderlyingSymbol = position.UnderlyingSymbol,
            EntryTime = position.EntryTime,
            EntryPrice = position.EntryValue,
            Quantity = signal.Legs.Sum(l => l.Quantity),
            Commission = 0.65m * signal.Legs.Count, // Assume $0.65 per contract
            Legs = signal.Legs,
            Metadata = new Dictionary<string, object>
            {
                ["Confidence"] = signal.Confidence,
                ["ExpectedProfit"] = signal.ExpectedProfit,
                ["MaxLoss"] = signal.MaxLoss
            }
        };
    }

    private void CalculateBacktestMetrics(BacktestResult result, BacktestPortfolio portfolio)
    {
        result.FinalCapital = portfolio.TotalValue;
        result.TotalReturn = (result.FinalCapital - result.InitialCapital) / result.InitialCapital;
        
        var tradingDays = result.DailyPerformance.Count;
        if (tradingDays > 0)
        {
            result.AnnualizedReturn = (decimal)Math.Pow((double)(1 + result.TotalReturn), 252.0 / tradingDays) - 1;
        }

        // Calculate trade statistics
        result.TotalTrades = result.Trades.Count;
        result.WinningTrades = result.Trades.Count(t => t.RealizedPnL > 0);
        result.LosingTrades = result.Trades.Count(t => t.RealizedPnL < 0);

        if (result.WinningTrades > 0)
            result.AverageWin = result.Trades.Where(t => t.RealizedPnL > 0).Average(t => t.RealizedPnL);
        
        if (result.LosingTrades > 0)
            result.AverageLoss = result.Trades.Where(t => t.RealizedPnL < 0).Average(t => t.RealizedPnL);

        result.LargestWin = result.Trades.Any() ? result.Trades.Max(t => t.RealizedPnL) : 0;
        result.LargestLoss = result.Trades.Any() ? result.Trades.Min(t => t.RealizedPnL) : 0;

        if (result.Trades.Any(t => t.ExitTime.HasValue))
        {
            result.AverageHoldingPeriod = TimeSpan.FromTicks((long)result.Trades
                .Where(t => t.ExitTime.HasValue)
                .Average(t => t.HoldingPeriod.Ticks));
        }

        // Calculate risk metrics
        if (result.DailyPerformance.Any())
        {
            var dailyReturns = result.DailyPerformance.Select(d => d.DailyReturn).ToList();
            var avgDailyReturn = dailyReturns.Average();
            var dailyStdDev = (decimal)Math.Sqrt((double)dailyReturns.Sum(r => (r - avgDailyReturn) * (r - avgDailyReturn)) / (dailyReturns.Count - 1));
            
            result.SharpeRatio = dailyStdDev > 0 ? (avgDailyReturn * (decimal)Math.Sqrt(252)) / (dailyStdDev * (decimal)Math.Sqrt(252)) : 0;
            
            // Calculate maximum drawdown
            var peak = result.InitialCapital;
            var maxDrawdown = 0m;
            
            foreach (var daily in result.DailyPerformance)
            {
                if (daily.PortfolioValue > peak)
                    peak = daily.PortfolioValue;
                
                var drawdown = (peak - daily.PortfolioValue) / peak;
                if (drawdown > maxDrawdown)
                    maxDrawdown = drawdown;
                
                daily.Drawdown = drawdown;
            }
            
            result.MaxDrawdown = maxDrawdown;
            result.CalmarRatio = result.MaxDrawdown > 0 ? result.AnnualizedReturn / result.MaxDrawdown : 0;
        }
    }

    private List<Dictionary<string, object>> GenerateParameterCombinations(Dictionary<string, List<object>> parameterRanges)
    {
        var combinations = new List<Dictionary<string, object>>();
        var keys = parameterRanges.Keys.ToList();
        
        GenerateCombinationsRecursive(parameterRanges, keys, 0, new Dictionary<string, object>(), combinations);
        
        return combinations;
    }

    private void GenerateCombinationsRecursive(
        Dictionary<string, List<object>> parameterRanges,
        List<string> keys,
        int keyIndex,
        Dictionary<string, object> currentCombination,
        List<Dictionary<string, object>> combinations)
    {
        if (keyIndex == keys.Count)
        {
            combinations.Add(new Dictionary<string, object>(currentCombination));
            return;
        }

        var key = keys[keyIndex];
        foreach (var value in parameterRanges[key])
        {
            currentCombination[key] = value;
            GenerateCombinationsRecursive(parameterRanges, keys, keyIndex + 1, currentCombination, combinations);
        }
    }

    private decimal GetOptimizationScore(BacktestResult result, string metric)
    {
        return metric.ToLower() switch
        {
            "sharpe" => result.SharpeRatio,
            "return" => result.TotalReturn,
            "calmar" => result.CalmarRatio,
            "winrate" => result.WinRate,
            _ => result.SharpeRatio
        };
    }
}

// Supporting classes
public class BacktestPortfolio
{
    public decimal InitialCapital { get; set; }
    public decimal Cash { get; set; }
    public decimal TotalValue { get; set; }
    public decimal DailyReturn { get; set; }
    public decimal DailyPnL { get; set; }

    public BacktestPortfolio(decimal initialCapital)
    {
        InitialCapital = initialCapital;
        Cash = initialCapital;
        TotalValue = initialCapital;
    }
}

public class BacktestPosition
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public string Strategy { get; set; } = string.Empty;
    public string UnderlyingSymbol { get; set; } = string.Empty;
    public List<OptionLeg> Legs { get; set; } = new();
    public DateTime EntryTime { get; set; }
    public DateTime? ExitTime { get; set; }
    public decimal EntryValue { get; set; }
    public decimal? ExitValue { get; set; }
    public decimal CurrentValue { get; set; }
    public decimal UnrealizedPnL { get; set; }
    public decimal RealizedPnL { get; set; }
    public decimal ProfitTarget { get; set; }
    public decimal StopLoss { get; set; }
    public DateTime ExpirationDate { get; set; }
    public PositionStatus Status { get; set; } = PositionStatus.Open;
    public string ExitReason { get; set; } = string.Empty;
}

public class BacktestConfiguration
{
    public string StrategyName { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public decimal InitialCapital { get; set; } = 10000m;
    public List<string> Symbols { get; set; } = new();
    public Dictionary<string, object> Parameters { get; set; } = new();
    public decimal Commission { get; set; } = 0.65m; // Per contract
}

public class ParameterOptimizationConfig
{
    public string StrategyName { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public decimal InitialCapital { get; set; } = 10000m;
    public List<string> Symbols { get; set; } = new();
    public Dictionary<string, List<object>> ParameterRanges { get; set; } = new();
    public string OptimizationMetric { get; set; } = "sharpe"; // sharpe, return, calmar, winrate
}

public class WalkForwardConfig
{
    public string StrategyName { get; set; } = string.Empty;
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public decimal InitialCapital { get; set; } = 10000m;
    public List<string> Symbols { get; set; } = new();
    public int InSampleDays { get; set; } = 60; // Days for optimization
    public int OutOfSampleDays { get; set; } = 20; // Days for testing
    public Dictionary<string, List<object>> ParameterRanges { get; set; } = new();
    public string OptimizationMetric { get; set; } = "sharpe";
}
